using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Interfaces
{
    /// <summary>
    /// Interface for delivering flow results and streaming data to various integration endpoints.
    /// Supports websockets, long HTTP requests, message queues, and webhooks.
    /// </summary>
    public interface IFlowResultDelivery
    {
        /// <summary>
        /// Registers a delivery endpoint for a flow execution session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="endpoint">The delivery endpoint configuration</param>
        /// <returns>True if registration was successful</returns>
        Task<bool> RegisterDeliveryEndpointAsync(string sessionId, FlowDeliveryEndpoint endpoint);

        /// <summary>
        /// Unregisters a delivery endpoint for a flow execution session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="endpointId">The endpoint ID to unregister</param>
        /// <returns>True if unregistration was successful</returns>
        Task<bool> UnregisterDeliveryEndpointAsync(string sessionId, string endpointId);

        /// <summary>
        /// Delivers a flow result to all registered endpoints for a session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="result">The flow result to deliver</param>
        /// <returns>Delivery results for each endpoint</returns>
        Task<IList<DeliveryResult>> DeliverFlowResultAsync(string sessionId, FlowExecutionResult result);

        /// <summary>
        /// Delivers streaming data to all registered endpoints for a session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="streamData">The streaming data to deliver</param>
        /// <returns>Delivery results for each endpoint</returns>
        Task<IList<DeliveryResult>> DeliverStreamDataAsync(string sessionId, FlowStreamData streamData);

        /// <summary>
        /// Delivers a status update to all registered endpoints for a session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="statusUpdate">The status update to deliver</param>
        /// <returns>Delivery results for each endpoint</returns>
        Task<IList<DeliveryResult>> DeliverStatusUpdateAsync(string sessionId, FlowStatusUpdate statusUpdate);

        /// <summary>
        /// Gets all registered delivery endpoints for a session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <returns>List of registered endpoints</returns>
        Task<IList<FlowDeliveryEndpoint>> GetDeliveryEndpointsAsync(string sessionId);

        /// <summary>
        /// Tests connectivity to a delivery endpoint.
        /// </summary>
        /// <param name="endpoint">The endpoint to test</param>
        /// <returns>Test result</returns>
        Task<EndpointTestResult> TestEndpointAsync(FlowDeliveryEndpoint endpoint);

        /// <summary>
        /// Event fired when delivery to an endpoint fails.
        /// </summary>
        event EventHandler<DeliveryFailedEventArgs>? OnDeliveryFailed;

        /// <summary>
        /// Event fired when delivery to an endpoint succeeds.
        /// </summary>
        event EventHandler<DeliverySucceededEventArgs>? OnDeliverySucceeded;
    }

    /// <summary>
    /// Configuration for a flow result delivery endpoint.
    /// </summary>
    public class FlowDeliveryEndpoint
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public FlowDeliveryType Type { get; set; }
        public string Url { get; set; } = string.Empty;
        public Dictionary<string, string> Headers { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        public bool DeliverResults { get; set; } = true;
        public bool DeliverStreaming { get; set; } = true;
        public bool DeliverStatusUpdates { get; set; } = true;
        public int MaxRetries { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(5);
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Types of delivery endpoints.
    /// </summary>
    public enum FlowDeliveryType
    {
        WebSocket,
        LongHttp,
        Webhook,
        MessageQueue,
        EventBus,
        Custom
    }

    /// <summary>
    /// Result of a delivery attempt.
    /// </summary>
    public class DeliveryResult
    {
        public string EndpointId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan DeliveryTime { get; set; }
        public int HttpStatusCode { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Result of testing an endpoint.
    /// </summary>
    public class EndpointTestResult
    {
        public bool IsReachable { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public int HttpStatusCode { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Event arguments for delivery failures.
    /// </summary>
    public class DeliveryFailedEventArgs : EventArgs
    {
        public string SessionId { get; set; } = string.Empty;
        public string EndpointId { get; set; } = string.Empty;
        public Exception Exception { get; set; } = new();
        public int RetryCount { get; set; }
        public bool WillRetry { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Event arguments for delivery successes.
    /// </summary>
    public class DeliverySucceededEventArgs : EventArgs
    {
        public string SessionId { get; set; } = string.Empty;
        public string EndpointId { get; set; } = string.Empty;
        public TimeSpan DeliveryTime { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
