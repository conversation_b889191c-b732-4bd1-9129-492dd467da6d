using System.Text.Json.Serialization;

namespace shared.Components.FlowBuilder.Models
{
    /// <summary>
    /// Core model classes for FlowBuilder execution and state management.
    /// </summary>

    /// <summary>
    /// Represents the execution context for a flow, containing state and field access.
    /// </summary>
    public class FlowExecutionContext
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public string AccountId { get; set; } = string.Empty;
        public Dictionary<string, object> InputFields { get; set; } = new();
        public Dictionary<string, object> VariableFields { get; set; } = new();
        public Dictionary<string, object> ExecutionData { get; set; } = new();
        public DateTime StartedAt { get; set; } = DateTime.UtcNow;
        public string? UserId { get; set; }
        public Dictionary<string, string> Headers { get; set; } = new();

        /// <summary>
        /// Gets the value of a field (input or variable).
        /// </summary>
        public object? GetFieldValue(string fieldName)
        {
            if (InputFields.ContainsKey(fieldName))
                return InputFields[fieldName];
            if (VariableFields.ContainsKey(fieldName))
                return VariableFields[fieldName];
            return null;
        }

        /// <summary>
        /// Sets the value of a variable field (input fields cannot be changed).
        /// </summary>
        public bool SetFieldValue(string fieldName, object value)
        {
            if (InputFields.ContainsKey(fieldName))
                return false; // Input fields cannot be changed
            
            VariableFields[fieldName] = value;
            return true;
        }
    }

    /// <summary>
    /// Represents the current execution state of a flow.
    /// </summary>
    public class FlowExecutionState
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public FlowExecutionStatus Status { get; set; } = FlowExecutionStatus.NotStarted;
        public string? CurrentNodeId { get; set; }
        public List<string> CompletedNodeIds { get; set; } = new();
        public List<string> FailedNodeIds { get; set; } = new();
        public Dictionary<string, NodeExecutionResult> NodeResults { get; set; } = new();
        public Dictionary<string, TaskExecutionResult> TaskResults { get; set; } = new();
        public Dictionary<string, object> FieldValues { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; } = DateTime.UtcNow;
        public int ExecutionStepCount { get; set; } = 0;
    }

    /// <summary>
    /// Execution status enumeration.
    /// </summary>
    public enum FlowExecutionStatus
    {
        NotStarted,
        Running,
        Paused,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// Result of a flow execution.
    /// </summary>
    public class FlowExecutionResult
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public FlowExecutionStatus Status { get; set; }
        public Dictionary<string, object> OutputFields { get; set; } = new();
        public Dictionary<string, object> FinalFieldValues { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
        public List<string> ExecutedNodeIds { get; set; } = new();
        public Dictionary<string, object> ExecutionMetadata { get; set; } = new();
    }

    /// <summary>
    /// Result of a node execution.
    /// </summary>
    public class NodeExecutionResult
    {
        public string NodeId { get; set; } = string.Empty;
        public NodeExecutionStatus Status { get; set; }
        public Dictionary<string, object> OutputData { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
        public List<string> NextNodeIds { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Node execution status enumeration.
    /// </summary>
    public enum NodeExecutionStatus
    {
        NotStarted,
        Running,
        Completed,
        Failed,
        Skipped,
        Cancelled
    }

    /// <summary>
    /// Result of a task execution.
    /// </summary>
    public class TaskExecutionResult
    {
        public string TaskId { get; set; } = string.Empty;
        public TaskExecutionStatus Status { get; set; }
        public Dictionary<string, object> OutputData { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Metadata { get; set; } = new();
        public bool AffectsFlowExecution { get; set; } = false;
    }

    /// <summary>
    /// Task execution status enumeration.
    /// </summary>
    public enum TaskExecutionStatus
    {
        NotStarted,
        Running,
        Completed,
        Failed,
        Cancelled,
        LongRunning
    }

    /// <summary>
    /// Validation context for flows, nodes, and tasks.
    /// </summary>
    public class FlowValidationContext
    {
        public Flow Flow { get; set; } = new();
        public Dictionary<string, FlowField> AvailableFields { get; set; } = new();
        public Dictionary<string, FlowNode> AllNodes { get; set; } = new();
        public List<FlowEdge> AllEdges { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// Result of flow validation.
    /// </summary>
    public class FlowValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, NodeValidationResult> NodeResults { get; set; } = new();
        public Dictionary<string, TaskValidationResult> TaskResults { get; set; } = new();
    }

    /// <summary>
    /// Result of node validation.
    /// </summary>
    public class NodeValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// Result of task validation.
    /// </summary>
    public class TaskValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// Streaming data from flow execution.
    /// </summary>
    public class FlowStreamData
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public string StreamType { get; set; } = string.Empty;
        public object Data { get; set; } = new();
        public string? SourceNodeId { get; set; }
        public string? SourceTaskId { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Status update for flow execution.
    /// </summary>
    public class FlowStatusUpdate
    {
        public string SessionId { get; set; } = string.Empty;
        public string FlowId { get; set; } = string.Empty;
        public FlowExecutionStatus Status { get; set; }
        public string? Message { get; set; }
        public string? CurrentNodeId { get; set; }
        public string? CurrentTaskId { get; set; }
        public int ProgressPercentage { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
