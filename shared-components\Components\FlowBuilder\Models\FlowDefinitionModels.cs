using System.Text.Json.Serialization;

namespace shared.Components.FlowBuilder.Models
{
    /// <summary>
    /// Definition models for flow nodes and tasks that contain configuration data.
    /// </summary>

    /// <summary>
    /// Base definition for flow nodes.
    /// </summary>
    public class FlowNodeDefinition
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public FlowPosition Position { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        public FlowRuleGroup? Conditions { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Gets a configuration value with type conversion.
        /// </summary>
        public T? GetConfigValue<T>(string key, T? defaultValue = default)
        {
            if (!Configuration.ContainsKey(key))
                return defaultValue;

            try
            {
                var value = Configuration[key];
                if (value is T directValue)
                    return directValue;

                return (T?)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// Sets a configuration value.
        /// </summary>
        public void SetConfigValue(string key, object value)
        {
            Configuration[key] = value;
        }
    }

    /// <summary>
    /// Base definition for flow tasks.
    /// </summary>
    public class FlowTaskDefinition
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public Dictionary<string, object> Configuration { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Gets a configuration value with type conversion.
        /// </summary>
        public T? GetConfigValue<T>(string key, T? defaultValue = default)
        {
            if (!Configuration.ContainsKey(key))
                return defaultValue;

            try
            {
                var value = Configuration[key];
                if (value is T directValue)
                    return directValue;

                return (T?)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// Sets a configuration value.
        /// </summary>
        public void SetConfigValue(string key, object value)
        {
            Configuration[key] = value;
        }
    }

    /// <summary>
    /// Specific node definitions based on the documentation.
    /// </summary>

    /// <summary>
    /// Set Agent Node configuration.
    /// </summary>
    public class SetAgentNodeDefinition : FlowNodeDefinition
    {
        public string AgentId { get; set; } = string.Empty;
        public bool IsAlias { get; set; } = false;
        public string AgentAliasOrTag { get; set; } = string.Empty;

        public SetAgentNodeDefinition()
        {
            Type = "SetAgent";
        }
    }

    /// <summary>
    /// Task Node configuration.
    /// </summary>
    public class TaskNodeDefinition : FlowNodeDefinition
    {
        public List<FlowTaskDefinition> Tasks { get; set; } = new();

        public TaskNodeDefinition()
        {
            Type = "Task";
        }
    }

    /// <summary>
    /// Sequence Node configuration.
    /// </summary>
    public class SequenceNodeDefinition : FlowNodeDefinition
    {
        public int OutputCount { get; set; } = 2;

        public SequenceNodeDefinition()
        {
            Type = "Sequence";
        }
    }

    /// <summary>
    /// Parallel Node configuration.
    /// </summary>
    public class ParallelNodeDefinition : FlowNodeDefinition
    {
        public int OutputCount { get; set; } = 2;

        public ParallelNodeDefinition()
        {
            Type = "Parallel";
        }
    }

    /// <summary>
    /// Select Node configuration.
    /// </summary>
    public class SelectNodeDefinition : FlowNodeDefinition
    {
        public int OutputCount { get; set; } = 2;

        public SelectNodeDefinition()
        {
            Type = "Select";
        }
    }

    /// <summary>
    /// Loop Node configuration.
    /// </summary>
    public class LoopNodeDefinition : FlowNodeDefinition
    {
        public FlowRuleGroup? LoopCondition { get; set; }

        public LoopNodeDefinition()
        {
            Type = "Loop";
        }
    }

    /// <summary>
    /// Switch Node configuration.
    /// </summary>
    public class SwitchNodeDefinition : FlowNodeDefinition
    {
        public List<SwitchCondition> Conditions { get; set; } = new();

        public SwitchNodeDefinition()
        {
            Type = "Switch";
        }
    }

    /// <summary>
    /// Switch condition definition.
    /// </summary>
    public class SwitchCondition
    {
        public string Id { get; set; } = string.Empty;
        public FlowRuleGroup Condition { get; set; } = new();
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// Specific task definitions based on the documentation.
    /// </summary>

    /// <summary>
    /// Dummy Task configuration.
    /// </summary>
    public class DummyTaskDefinition : FlowTaskDefinition
    {
        public string Message { get; set; } = string.Empty;

        public DummyTaskDefinition()
        {
            Type = "DummyTask";
        }
    }

    /// <summary>
    /// Conditional Task configuration.
    /// </summary>
    public class ConditionalTaskDefinition : FlowTaskDefinition
    {
        public List<ConditionalClause> Clauses { get; set; } = new();
        public List<FlowTaskDefinition> ElseTasks { get; set; } = new();

        public ConditionalTaskDefinition()
        {
            Type = "ConditionalTask";
        }
    }

    /// <summary>
    /// Conditional clause for conditional tasks.
    /// </summary>
    public class ConditionalClause
    {
        public FlowRuleGroup Condition { get; set; } = new();
        public List<FlowTaskDefinition> Tasks { get; set; } = new();
    }

    /// <summary>
    /// AWS Lambda Task configuration (for cloud provider integrations).
    /// </summary>
    public class LambdaTaskDefinition : FlowTaskDefinition
    {
        public string FunctionName { get; set; } = string.Empty;
        public string Region { get; set; } = string.Empty;
        public Dictionary<string, object> Payload { get; set; } = new();
        public bool IsAsync { get; set; } = false;
        public int TimeoutSeconds { get; set; } = 30;

        public LambdaTaskDefinition()
        {
            Type = "LambdaTask";
        }
    }

    /// <summary>
    /// HTTP Request Task configuration (for API integrations).
    /// </summary>
    public class HttpRequestTaskDefinition : FlowTaskDefinition
    {
        public string Url { get; set; } = string.Empty;
        public string Method { get; set; } = "GET";
        public Dictionary<string, string> Headers { get; set; } = new();
        public object? Body { get; set; }
        public int TimeoutSeconds { get; set; } = 30;
        public bool FollowRedirects { get; set; } = true;

        public HttpRequestTaskDefinition()
        {
            Type = "HttpRequestTask";
        }
    }

    /// <summary>
    /// Database Query Task configuration (for database integrations).
    /// </summary>
    public class DatabaseQueryTaskDefinition : FlowTaskDefinition
    {
        public string ConnectionString { get; set; } = string.Empty;
        public string Query { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public int TimeoutSeconds { get; set; } = 30;

        public DatabaseQueryTaskDefinition()
        {
            Type = "DatabaseQueryTask";
        }
    }

    /// <summary>
    /// Message Queue Task configuration (for message queue integrations).
    /// </summary>
    public class MessageQueueTaskDefinition : FlowTaskDefinition
    {
        public string QueueName { get; set; } = string.Empty;
        public object Message { get; set; } = new();
        public Dictionary<string, string> MessageAttributes { get; set; } = new();
        public int DelaySeconds { get; set; } = 0;

        public MessageQueueTaskDefinition()
        {
            Type = "MessageQueueTask";
        }
    }
}
