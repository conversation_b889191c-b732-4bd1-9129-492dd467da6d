using Microsoft.Extensions.DependencyInjection;
using shared.Components.FlowBuilder.Engine;
using shared.Components.FlowBuilder.EventBus;
using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using shared.Components.FlowBuilder.ResultDelivery;
using shared.Components.FlowBuilder.StateService;
using shared.Components.FlowBuilder.Tasks;
using shared.Components.FlowBuilder.Nodes;

namespace shared.Components.FlowBuilder.Extensions
{
    /// <summary>
    /// Extension methods for registering FlowBuilder services with dependency injection.
    /// </summary>
    public static class FlowBuilderServiceExtensions
    {
        /// <summary>
        /// Add FlowBuilder services to the service collection.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="instanceName">Name for this FlowBuilder instance</param>
        /// <param name="useInMemoryState">Whether to use in-memory state service (default: true)</param>
        /// <returns>FlowBuilder configuration builder</returns>
        public static IFlowBuilderConfiguration AddFlowBuilder(
            this IServiceCollection services,
            string instanceName,
            bool useInMemoryState = true)
        {
            var instanceId = Guid.NewGuid().ToString();

            // Register core services
            services.AddSingleton<IGenericEventBus, GenericEventBus>();
            services.AddSingleton<IFlowResultDelivery, FlowResultDeliveryService>();

            // Register state service
            if (useInMemoryState)
            {
                services.AddSingleton<IFlowStateService, InMemoryFlowStateService>();
            }
            else
            {
                services.AddSingleton<IFlowStateService, NoSQLFlowStateService>();
            }

            // Register flow engine
            services.AddSingleton<IFlowEngine>(provider =>
            {
                var stateService = provider.GetRequiredService<IFlowStateService>();
                return new FlowEngine(stateService);
            });

            // Register FlowBuilder instance
            services.AddSingleton<IFlowBuilder>(provider =>
            {
                var flowEngine = provider.GetRequiredService<IFlowEngine>();
                return new FlowBuilderComponent(instanceId, instanceName, flowEngine);
            });

            return new FlowBuilderConfiguration(services, instanceId, instanceName);
        }

        /// <summary>
        /// Add a named FlowBuilder instance to the service collection.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="instanceName">Name for this FlowBuilder instance</param>
        /// <param name="useInMemoryState">Whether to use in-memory state service</param>
        /// <returns>FlowBuilder configuration builder</returns>
        public static IFlowBuilderConfiguration AddNamedFlowBuilder(
            this IServiceCollection services,
            string instanceName,
            bool useInMemoryState = true)
        {
            var instanceId = Guid.NewGuid().ToString();

            // Register named services
            services.AddSingleton<IGenericEventBus>(provider => new GenericEventBus());
            services.AddSingleton<IFlowResultDelivery>(provider => new FlowResultDeliveryService());

            // Register named state service
            if (useInMemoryState)
            {
                services.AddSingleton<IFlowStateService>(provider => new InMemoryFlowStateService());
            }
            else
            {
                services.AddSingleton<IFlowStateService>(provider =>
                {
                    // This would need to be configured with proper repositories
                    throw new NotImplementedException("NoSQL state service requires repository configuration");
                });
            }

            // Register named flow engine
            services.AddSingleton<IFlowEngine>(provider =>
            {
                var stateService = provider.GetRequiredService<IFlowStateService>();
                return new FlowEngine(stateService);
            });

            // Register named FlowBuilder instance
            services.AddSingleton<IFlowBuilder>(provider =>
            {
                var flowEngine = provider.GetRequiredService<IFlowEngine>();
                return new FlowBuilderComponent(instanceId, instanceName, flowEngine);
            });

            return new FlowBuilderConfiguration(services, instanceId, instanceName);
        }
    }

    /// <summary>
    /// Configuration builder for FlowBuilder services.
    /// </summary>
    public interface IFlowBuilderConfiguration
    {
        /// <summary>
        /// Register a custom node type.
        /// </summary>
        IFlowBuilderConfiguration RegisterNode<TNode>(string nodeType) where TNode : class, IFlowNode, new();

        /// <summary>
        /// Register a custom node type with factory.
        /// </summary>
        IFlowBuilderConfiguration RegisterNode(string nodeType, Func<FlowNodeDefinition, IFlowNode> factory);

        /// <summary>
        /// Register a custom task type.
        /// </summary>
        IFlowBuilderConfiguration RegisterTask<TTask>(string taskType) where TTask : class, IFlowTask, new();

        /// <summary>
        /// Register a custom task type with factory.
        /// </summary>
        IFlowBuilderConfiguration RegisterTask(string taskType, Func<FlowTaskDefinition, IFlowTask> factory);

        /// <summary>
        /// Configure the event bus.
        /// </summary>
        IFlowBuilderConfiguration ConfigureEventBus(Action<IGenericEventBus> configure);

        /// <summary>
        /// Configure the result delivery service.
        /// </summary>
        IFlowBuilderConfiguration ConfigureResultDelivery(Action<IFlowResultDelivery> configure);

        /// <summary>
        /// Get the service collection for additional configuration.
        /// </summary>
        IServiceCollection Services { get; }
    }

    /// <summary>
    /// Implementation of FlowBuilder configuration builder.
    /// </summary>
    internal class FlowBuilderConfiguration : IFlowBuilderConfiguration
    {
        public IServiceCollection Services { get; }
        public string InstanceId { get; }
        public string InstanceName { get; }

        private readonly List<Action<IFlowBuilder>> _configurationActions = new();

        public FlowBuilderConfiguration(IServiceCollection services, string instanceId, string instanceName)
        {
            Services = services;
            InstanceId = instanceId;
            InstanceName = instanceName;

            // Register configuration actions to be applied after FlowBuilder is created
            Services.AddSingleton<IFlowBuilderConfigurator>(provider =>
                new FlowBuilderConfigurator(_configurationActions));
        }

        public IFlowBuilderConfiguration RegisterNode<TNode>(string nodeType) where TNode : class, IFlowNode, new()
        {
            _configurationActions.Add(flowBuilder =>
            {
                flowBuilder.RegisterCustomNode(nodeType, def => new TNode());
            });

            return this;
        }

        public IFlowBuilderConfiguration RegisterNode(string nodeType, Func<FlowNodeDefinition, IFlowNode> factory)
        {
            _configurationActions.Add(flowBuilder =>
            {
                flowBuilder.RegisterCustomNode(nodeType, factory);
            });

            return this;
        }

        public IFlowBuilderConfiguration RegisterTask<TTask>(string taskType) where TTask : class, IFlowTask, new()
        {
            _configurationActions.Add(flowBuilder =>
            {
                flowBuilder.RegisterCustomTask(taskType, def => new TTask());
            });

            return this;
        }

        public IFlowBuilderConfiguration RegisterTask(string taskType, Func<FlowTaskDefinition, IFlowTask> factory)
        {
            _configurationActions.Add(flowBuilder =>
            {
                flowBuilder.RegisterCustomTask(taskType, factory);
            });

            return this;
        }

        public IFlowBuilderConfiguration ConfigureEventBus(Action<IGenericEventBus> configure)
        {
            Services.AddSingleton<IGenericEventBusConfigurator>(provider =>
                new GenericEventBusConfigurator(configure));

            return this;
        }

        public IFlowBuilderConfiguration ConfigureResultDelivery(Action<IFlowResultDelivery> configure)
        {
            Services.AddSingleton<IFlowResultDeliveryConfigurator>(provider =>
                new FlowResultDeliveryConfigurator(configure));

            return this;
        }
    }

    /// <summary>
    /// Interface for configuring FlowBuilder after creation.
    /// </summary>
    public interface IFlowBuilderConfigurator
    {
        void Configure(IFlowBuilder flowBuilder);
    }

    /// <summary>
    /// Implementation of FlowBuilder configurator.
    /// </summary>
    internal class FlowBuilderConfigurator : IFlowBuilderConfigurator
    {
        private readonly List<Action<IFlowBuilder>> _configurationActions;

        public FlowBuilderConfigurator(List<Action<IFlowBuilder>> configurationActions)
        {
            _configurationActions = configurationActions;
        }

        public void Configure(IFlowBuilder flowBuilder)
        {
            foreach (var action in _configurationActions)
            {
                action(flowBuilder);
            }
        }
    }

    /// <summary>
    /// Interface for configuring Generic Event Bus.
    /// </summary>
    public interface IGenericEventBusConfigurator
    {
        void Configure(IGenericEventBus eventBus);
    }

    /// <summary>
    /// Implementation of Generic Event Bus configurator.
    /// </summary>
    internal class GenericEventBusConfigurator : IGenericEventBusConfigurator
    {
        private readonly Action<IGenericEventBus> _configureAction;

        public GenericEventBusConfigurator(Action<IGenericEventBus> configureAction)
        {
            _configureAction = configureAction;
        }

        public void Configure(IGenericEventBus eventBus)
        {
            _configureAction(eventBus);
        }
    }

    /// <summary>
    /// Interface for configuring Flow Result Delivery.
    /// </summary>
    public interface IFlowResultDeliveryConfigurator
    {
        void Configure(IFlowResultDelivery resultDelivery);
    }

    /// <summary>
    /// Implementation of Flow Result Delivery configurator.
    /// </summary>
    internal class FlowResultDeliveryConfigurator : IFlowResultDeliveryConfigurator
    {
        private readonly Action<IFlowResultDelivery> _configureAction;

        public FlowResultDeliveryConfigurator(Action<IFlowResultDelivery> configureAction)
        {
            _configureAction = configureAction;
        }

        public void Configure(IFlowResultDelivery resultDelivery)
        {
            _configureAction(resultDelivery);
        }
    }
}
