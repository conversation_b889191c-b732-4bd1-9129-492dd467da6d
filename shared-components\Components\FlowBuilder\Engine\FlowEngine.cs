using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using shared.Models.Documents.DynamoDB;
using System.Collections.Concurrent;

namespace shared.Components.FlowBuilder.Engine
{
    /// <summary>
    /// Core engine for executing flows. Handles node traversal, state management,
    /// and execution coordination.
    /// </summary>
    public class FlowEngine : IFlowEngine
    {
        private readonly IFlowStateService _stateService;
        private readonly Dictionary<string, Func<FlowNodeDefinition, IFlowNode>> _nodeFactories;
        private readonly Dictionary<string, Func<FlowTaskDefinition, IFlowTask>> _taskFactories;
        private readonly ConcurrentDictionary<string, FlowExecutionSession> _activeSessions;

        public event EventHandler<FlowExecutionStatusChangedEventArgs>? OnExecutionStatusChanged;
        public event EventHandler<NodeExecutionEventArgs>? OnNodeExecutionStarted;
        public event EventHandler<NodeExecutionEventArgs>? OnNodeExecutionCompleted;
        public event EventHandler<TaskExecutionEventArgs>? OnTaskExecutionStarted;
        public event EventHandler<TaskExecutionEventArgs>? OnTaskExecutionCompleted;
        public event EventHandler<FlowStreamEventArgs>? OnStreamResult;

        public FlowEngine(
            IFlowStateService stateService,
            Dictionary<string, Func<FlowNodeDefinition, IFlowNode>>? nodeFactories = null,
            Dictionary<string, Func<FlowTaskDefinition, IFlowTask>>? taskFactories = null)
        {
            _stateService = stateService;
            _nodeFactories = nodeFactories ?? new Dictionary<string, Func<FlowNodeDefinition, IFlowNode>>();
            _taskFactories = taskFactories ?? new Dictionary<string, Func<FlowTaskDefinition, IFlowTask>>();
            _activeSessions = new ConcurrentDictionary<string, FlowExecutionSession>();
        }

        public async Task<FlowExecutionResult> ExecuteFlowAsync(Flow flow, FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var sessionId = await StartFlowExecutionSessionAsync(flow, context);
            
            try
            {
                return await ContinueExecutionAsync(sessionId, cancellationToken);
            }
            catch (Exception ex)
            {
                // Update session state to failed
                await UpdateSessionStatus(sessionId, FlowExecutionStatus.Failed, ex.Message);
                
                return new FlowExecutionResult
                {
                    SessionId = sessionId,
                    FlowId = flow.Id,
                    Status = FlowExecutionStatus.Failed,
                    ErrorMessage = ex.Message,
                    Exception = ex,
                    CompletedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<string> StartFlowExecutionSessionAsync(Flow flow, FlowExecutionContext context)
        {
            var sessionId = context.SessionId;
            if (string.IsNullOrEmpty(sessionId))
            {
                sessionId = Guid.NewGuid().ToString();
                context.SessionId = sessionId;
            }

            // Create execution state
            var executionState = new FlowExecutionState
            {
                SessionId = sessionId,
                FlowId = flow.Id,
                Status = FlowExecutionStatus.NotStarted,
                FieldValues = new Dictionary<string, object>()
            };

            // Merge input and variable fields
            foreach (var field in context.InputFields)
                executionState.FieldValues[field.Key] = field.Value;
            foreach (var field in context.VariableFields)
                executionState.FieldValues[field.Key] = field.Value;

            // Store initial state
            await _stateService.StoreExecutionStateAsync(sessionId, executionState);
            await _stateService.StoreFieldValuesAsync(sessionId, executionState.FieldValues);

            // Create execution session
            var session = new FlowExecutionSession
            {
                SessionId = sessionId,
                Flow = flow,
                Context = context,
                State = executionState,
                StartedAt = DateTime.UtcNow
            };

            _activeSessions[sessionId] = session;

            // Fire status changed event
            OnExecutionStatusChanged?.Invoke(this, new FlowExecutionStatusChangedEventArgs
            {
                SessionId = sessionId,
                FlowId = flow.Id,
                OldStatus = FlowExecutionStatus.NotStarted,
                NewStatus = FlowExecutionStatus.NotStarted,
                Message = "Flow execution session created"
            });

            return sessionId;
        }

        public async Task<FlowExecutionResult> ContinueExecutionAsync(string sessionId, CancellationToken cancellationToken = default)
        {
            if (!_activeSessions.TryGetValue(sessionId, out var session))
            {
                var state = await _stateService.GetExecutionStateAsync(sessionId);
                if (state == null)
                {
                    throw new InvalidOperationException($"Execution session {sessionId} not found");
                }

                // Reconstruct session from state (simplified)
                session = new FlowExecutionSession
                {
                    SessionId = sessionId,
                    State = state
                };
                _activeSessions[sessionId] = session;
            }

            try
            {
                await UpdateSessionStatus(sessionId, FlowExecutionStatus.Running);

                // Get starting nodes if this is the first execution
                var currentNodes = new List<string>();
                if (session.State.CurrentNodeId == null)
                {
                    var startingNodes = session.Flow?.GetStartingNodes() ?? new List<FlowNode>();
                    currentNodes = startingNodes.Select(n => n.Id).ToList();
                }
                else
                {
                    currentNodes.Add(session.State.CurrentNodeId);
                }

                // Execute nodes
                var result = await ExecuteNodesAsync(session, currentNodes, cancellationToken);

                // Update final state
                if (result.Status == FlowExecutionStatus.Completed || result.Status == FlowExecutionStatus.Failed)
                {
                    await UpdateSessionStatus(sessionId, result.Status, result.ErrorMessage);
                    session.State.CompletedAt = DateTime.UtcNow;
                    _activeSessions.TryRemove(sessionId, out _);
                }

                return result;
            }
            catch (OperationCanceledException)
            {
                await UpdateSessionStatus(sessionId, FlowExecutionStatus.Cancelled);
                _activeSessions.TryRemove(sessionId, out _);
                
                return new FlowExecutionResult
                {
                    SessionId = sessionId,
                    FlowId = session.Flow?.Id ?? "",
                    Status = FlowExecutionStatus.Cancelled,
                    CompletedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                await UpdateSessionStatus(sessionId, FlowExecutionStatus.Failed, ex.Message);
                _activeSessions.TryRemove(sessionId, out _);
                
                return new FlowExecutionResult
                {
                    SessionId = sessionId,
                    FlowId = session.Flow?.Id ?? "",
                    Status = FlowExecutionStatus.Failed,
                    ErrorMessage = ex.Message,
                    Exception = ex,
                    CompletedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<FlowExecutionState> GetExecutionStateAsync(string sessionId)
        {
            if (_activeSessions.TryGetValue(sessionId, out var session))
            {
                return session.State;
            }

            var state = await _stateService.GetExecutionStateAsync(sessionId);
            return state ?? new FlowExecutionState { SessionId = sessionId, Status = FlowExecutionStatus.NotStarted };
        }

        public async Task<bool> PauseExecutionAsync(string sessionId)
        {
            if (_activeSessions.TryGetValue(sessionId, out var session))
            {
                session.IsPaused = true;
                await UpdateSessionStatus(sessionId, FlowExecutionStatus.Paused);
                return true;
            }
            return false;
        }

        public async Task<bool> ResumeExecutionAsync(string sessionId)
        {
            if (_activeSessions.TryGetValue(sessionId, out var session))
            {
                session.IsPaused = false;
                await UpdateSessionStatus(sessionId, FlowExecutionStatus.Running);
                return true;
            }
            return false;
        }

        public async Task<bool> CancelExecutionAsync(string sessionId)
        {
            if (_activeSessions.TryGetValue(sessionId, out var session))
            {
                session.CancellationTokenSource.Cancel();
                await UpdateSessionStatus(sessionId, FlowExecutionStatus.Cancelled);
                _activeSessions.TryRemove(sessionId, out _);
                return true;
            }
            return false;
        }

        /// <summary>
        /// Execute a list of nodes.
        /// </summary>
        private async Task<FlowExecutionResult> ExecuteNodesAsync(FlowExecutionSession session, List<string> nodeIds, CancellationToken cancellationToken)
        {
            var executedNodes = new List<string>();
            var hasFailures = false;
            string? errorMessage = null;

            foreach (var nodeId in nodeIds)
            {
                if (cancellationToken.IsCancellationRequested || session.IsPaused)
                {
                    break;
                }

                try
                {
                    var nodeResult = await ExecuteNodeAsync(session, nodeId, cancellationToken);
                    executedNodes.Add(nodeId);

                    if (nodeResult.Status == NodeExecutionStatus.Failed)
                    {
                        hasFailures = true;
                        errorMessage = nodeResult.ErrorMessage;
                        break;
                    }

                    // Get next nodes to execute
                    var nextNodes = await GetNextNodesFromResult(session, nodeResult);
                    if (nextNodes.Count > 0)
                    {
                        // Continue with next nodes (this could be recursive for complex flows)
                        var nextNodeIds = nextNodes.Select(n => n.NodeId).ToList();
                        var nextResult = await ExecuteNodesAsync(session, nextNodeIds, cancellationToken);
                        
                        if (nextResult.Status == FlowExecutionStatus.Failed)
                        {
                            hasFailures = true;
                            errorMessage = nextResult.ErrorMessage;
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    hasFailures = true;
                    errorMessage = ex.Message;
                    break;
                }
            }

            // Determine final status
            var finalStatus = hasFailures ? FlowExecutionStatus.Failed : 
                             cancellationToken.IsCancellationRequested ? FlowExecutionStatus.Cancelled :
                             session.IsPaused ? FlowExecutionStatus.Paused :
                             FlowExecutionStatus.Completed;

            // Get final field values
            var finalFieldValues = await _stateService.GetFieldValuesAsync(session.SessionId);

            return new FlowExecutionResult
            {
                SessionId = session.SessionId,
                FlowId = session.Flow?.Id ?? "",
                Status = finalStatus,
                FinalFieldValues = finalFieldValues,
                ErrorMessage = errorMessage,
                ExecutedNodeIds = executedNodes,
                CompletedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Execute a single node.
        /// </summary>
        private async Task<NodeExecutionResult> ExecuteNodeAsync(FlowExecutionSession session, string nodeId, CancellationToken cancellationToken)
        {
            var flowNode = session.Flow?.GetNode(nodeId);
            if (flowNode == null)
            {
                throw new InvalidOperationException($"Node {nodeId} not found in flow");
            }

            // Fire node execution started event
            OnNodeExecutionStarted?.Invoke(this, new NodeExecutionEventArgs
            {
                SessionId = session.SessionId,
                FlowId = session.Flow?.Id ?? "",
                NodeId = nodeId,
                NodeType = flowNode.Type,
                NodeName = flowNode.Name
            });

            // Create node instance
            if (!_nodeFactories.TryGetValue(flowNode.Type, out var nodeFactory))
            {
                throw new InvalidOperationException($"No factory registered for node type: {flowNode.Type}");
            }

            var nodeDefinition = ConvertToNodeDefinition(flowNode);
            var node = nodeFactory(nodeDefinition);
            
            if (!await node.InitializeAsync(nodeDefinition))
            {
                throw new InvalidOperationException($"Failed to initialize node {nodeId}");
            }

            // Execute node
            var result = await node.ExecuteAsync(session.Context, cancellationToken);

            // Store node result
            await _stateService.StoreNodeResultAsync(session.SessionId, nodeId, result);

            // Update session state
            session.State.CurrentNodeId = nodeId;
            session.State.ExecutionStepCount++;
            
            if (result.Status == NodeExecutionStatus.Completed)
            {
                session.State.CompletedNodeIds.Add(nodeId);
            }
            else if (result.Status == NodeExecutionStatus.Failed)
            {
                session.State.FailedNodeIds.Add(nodeId);
            }

            await _stateService.StoreExecutionStateAsync(session.SessionId, session.State);

            // Fire node execution completed event
            OnNodeExecutionCompleted?.Invoke(this, new NodeExecutionEventArgs
            {
                SessionId = session.SessionId,
                FlowId = session.Flow?.Id ?? "",
                NodeId = nodeId,
                NodeType = flowNode.Type,
                NodeName = flowNode.Name,
                Result = result
            });

            return result;
        }

        /// <summary>
        /// Get next nodes from execution result.
        /// </summary>
        private async Task<List<NextNodeInfo>> GetNextNodesFromResult(FlowExecutionSession session, NodeExecutionResult result)
        {
            // This is a simplified implementation
            // In a full implementation, this would use the flow edges to determine next nodes
            await Task.CompletedTask;
            return new List<NextNodeInfo>();
        }

        /// <summary>
        /// Convert FlowNode to FlowNodeDefinition.
        /// </summary>
        private FlowNodeDefinition ConvertToNodeDefinition(FlowNode flowNode)
        {
            return new FlowNodeDefinition
            {
                Id = flowNode.Id,
                Type = flowNode.Type,
                Name = flowNode.Name,
                Position = flowNode.Position,
                Configuration = flowNode.Data.NodeData,
                Conditions = flowNode.Data.BaseData.NodeConditions,
                Metadata = flowNode.Metadata
            };
        }

        /// <summary>
        /// Update session status.
        /// </summary>
        private async Task UpdateSessionStatus(string sessionId, FlowExecutionStatus status, string? message = null)
        {
            if (_activeSessions.TryGetValue(sessionId, out var session))
            {
                var oldStatus = session.State.Status;
                session.State.Status = status;
                session.State.LastUpdatedAt = DateTime.UtcNow;
                
                if (message != null)
                {
                    session.State.ErrorMessage = message;
                }

                await _stateService.StoreExecutionStateAsync(sessionId, session.State);

                OnExecutionStatusChanged?.Invoke(this, new FlowExecutionStatusChangedEventArgs
                {
                    SessionId = sessionId,
                    FlowId = session.Flow?.Id ?? "",
                    OldStatus = oldStatus,
                    NewStatus = status,
                    Message = message
                });
            }
        }
    }

    /// <summary>
    /// Internal class for tracking active execution sessions.
    /// </summary>
    internal class FlowExecutionSession
    {
        public string SessionId { get; set; } = string.Empty;
        public Flow? Flow { get; set; }
        public FlowExecutionContext Context { get; set; } = new();
        public FlowExecutionState State { get; set; } = new();
        public DateTime StartedAt { get; set; } = DateTime.UtcNow;
        public bool IsPaused { get; set; } = false;
        public CancellationTokenSource CancellationTokenSource { get; set; } = new();
    }
}
