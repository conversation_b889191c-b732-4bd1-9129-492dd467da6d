using shared.Components.FlowBuilder.Interfaces;
using shared.Components.FlowBuilder.Models;
using shared.Models.Documents.DynamoDB;
using shared.Services;
using System.Text.Json;

namespace shared.Components.FlowBuilder.StateService
{
    /// <summary>
    /// NoSQL implementation of IFlowStateService using DynamoDB.
    /// Suitable for production environments requiring persistence and scalability.
    /// </summary>
    public class NoSQLFlowStateService : IFlowStateService
    {
        private readonly INoSQLRepository<FlowExecutionDocument> _executionRepository;
        private readonly INoSQLRepository<FlowNodeExecutionDocument> _nodeRepository;
        private readonly INoSQLRepository<FlowTaskExecutionDocument> _taskRepository;

        public NoSQLFlowStateService(
            INoSQLRepository<FlowExecutionDocument> executionRepository,
            INoSQLRepository<FlowNodeExecutionDocument> nodeRepository,
            INoSQLRepository<FlowTaskExecutionDocument> taskRepository)
        {
            _executionRepository = executionRepository;
            _nodeRepository = nodeRepository;
            _taskRepository = taskRepository;
        }

        public async Task<bool> StoreExecutionStateAsync(string sessionId, FlowExecutionState state, TimeSpan? ttl = null)
        {
            try
            {
                // Try to get existing document first
                var existingDoc = await _executionRepository.GetAsync(state.SessionId);
                
                FlowExecutionDocument document;
                if (existingDoc != null)
                {
                    // Update existing document
                    document = existingDoc;
                    document.Status = state.Status;
                    document.ExecutionState = state;
                    document.StartedAt = state.StartedAt;
                    document.CompletedAt = state.CompletedAt;
                    document.ExecutionStepCount = state.ExecutionStepCount;
                    document.ErrorMessage = state.ErrorMessage;
                    
                    if (state.CompletedAt.HasValue && state.StartedAt.HasValue)
                    {
                        document.ExecutionTimeMs = (long)(state.CompletedAt.Value - state.StartedAt.Value).TotalMilliseconds;
                    }
                }
                else
                {
                    // Create new document
                    document = new FlowExecutionDocument
                    {
                        AccountId = "default", // This should come from context
                        SessionId = sessionId,
                        FlowId = state.FlowId,
                        Status = state.Status,
                        ExecutionState = state,
                        StartedAt = state.StartedAt,
                        CompletedAt = state.CompletedAt,
                        ExecutionStepCount = state.ExecutionStepCount,
                        ErrorMessage = state.ErrorMessage
                    };

                    if (state.CompletedAt.HasValue && state.StartedAt.HasValue)
                    {
                        document.ExecutionTimeMs = (long)(state.CompletedAt.Value - state.StartedAt.Value).TotalMilliseconds;
                    }
                }

                // Set TTL if specified
                if (ttl.HasValue)
                {
                    document.SetTTL(ttl.Value);
                }

                var result = await _executionRepository.PutAsync(document);
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<FlowExecutionState?> GetExecutionStateAsync(string sessionId)
        {
            try
            {
                var document = await _executionRepository.GetAsync(sessionId);
                return document?.ExecutionState;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> UpdateExecutionStateAsync(string sessionId, Dictionary<string, object> updates)
        {
            try
            {
                var document = await _executionRepository.GetAsync(sessionId);
                if (document == null)
                {
                    return false;
                }

                // Apply updates to the execution state
                var state = document.ExecutionState;
                foreach (var update in updates)
                {
                    var property = typeof(FlowExecutionState).GetProperty(update.Key);
                    if (property != null && property.CanWrite)
                    {
                        var convertedValue = Convert.ChangeType(update.Value, property.PropertyType);
                        property.SetValue(state, convertedValue);
                    }
                }

                state.LastUpdatedAt = DateTime.UtcNow;
                document.ExecutionState = state;

                var result = await _executionRepository.PutAsync(document);
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteExecutionStateAsync(string sessionId)
        {
            try
            {
                var success = await _executionRepository.DeleteAsync(sessionId);
                
                // Also clean up related node and task results
                // Note: In a production system, you might want to do this in a background job
                await CleanupRelatedDataAsync(sessionId);
                
                return success;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> StoreFieldValuesAsync(string sessionId, Dictionary<string, object> fields, TimeSpan? ttl = null)
        {
            try
            {
                var document = await _executionRepository.GetAsync(sessionId);
                if (document == null)
                {
                    return false;
                }

                document.VariableFields = fields;
                
                if (ttl.HasValue)
                {
                    document.SetTTL(ttl.Value);
                }

                var result = await _executionRepository.PutAsync(document);
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetFieldValuesAsync(string sessionId)
        {
            try
            {
                var document = await _executionRepository.GetAsync(sessionId);
                return document?.VariableFields ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }

        public async Task<bool> UpdateFieldValuesAsync(string sessionId, Dictionary<string, object> fieldUpdates)
        {
            try
            {
                var document = await _executionRepository.GetAsync(sessionId);
                if (document == null)
                {
                    return false;
                }

                foreach (var update in fieldUpdates)
                {
                    document.VariableFields[update.Key] = update.Value;
                }

                var result = await _executionRepository.PutAsync(document);
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<object?> GetFieldValueAsync(string sessionId, string fieldName)
        {
            try
            {
                var document = await _executionRepository.GetAsync(sessionId);
                return document?.VariableFields.GetValueOrDefault(fieldName);
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> SetFieldValueAsync(string sessionId, string fieldName, object value)
        {
            try
            {
                var document = await _executionRepository.GetAsync(sessionId);
                if (document == null)
                {
                    return false;
                }

                document.VariableFields[fieldName] = value;

                var result = await _executionRepository.PutAsync(document);
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> StoreNodeResultAsync(string sessionId, string nodeId, NodeExecutionResult result, TimeSpan? ttl = null)
        {
            try
            {
                var document = new FlowNodeExecutionDocument
                {
                    SessionId = sessionId,
                    NodeId = nodeId,
                    FlowId = result.Metadata.GetValueOrDefault("flowId", "")?.ToString() ?? "",
                    NodeType = result.Metadata.GetValueOrDefault("nodeType", "")?.ToString() ?? "",
                    NodeName = result.Metadata.GetValueOrDefault("nodeName", "")?.ToString() ?? "",
                    Status = result.Status,
                    OutputData = result.OutputData,
                    ErrorMessage = result.ErrorMessage,
                    CompletedAt = result.CompletedAt,
                    ExecutionTimeMs = (long)result.ExecutionTime.TotalMilliseconds,
                    NextNodeIds = result.NextNodeIds,
                    Metadata = result.Metadata
                };

                if (ttl.HasValue)
                {
                    document.TTL = DateTimeOffset.UtcNow.Add(ttl.Value).ToUnixTimeSeconds();
                }

                var savedResult = await _nodeRepository.PutAsync(document);
                return savedResult != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<NodeExecutionResult?> GetNodeResultAsync(string sessionId, string nodeId)
        {
            try
            {
                var document = await _nodeRepository.GetAsync(sessionId, nodeId);
                return document?.ToExecutionResult();
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> StoreTaskResultAsync(string sessionId, string taskId, TaskExecutionResult result, TimeSpan? ttl = null)
        {
            try
            {
                var document = new FlowTaskExecutionDocument
                {
                    SessionId = sessionId,
                    TaskId = taskId,
                    FlowId = result.Metadata.GetValueOrDefault("flowId", "")?.ToString() ?? "",
                    NodeId = result.Metadata.GetValueOrDefault("nodeId", "")?.ToString() ?? "",
                    TaskType = result.Metadata.GetValueOrDefault("taskType", "")?.ToString() ?? "",
                    TaskName = result.Metadata.GetValueOrDefault("taskName", "")?.ToString() ?? "",
                    Status = result.Status,
                    OutputData = result.OutputData,
                    ErrorMessage = result.ErrorMessage,
                    CompletedAt = result.CompletedAt,
                    ExecutionTimeMs = (long)result.ExecutionTime.TotalMilliseconds,
                    AffectsFlowExecution = result.AffectsFlowExecution,
                    Metadata = result.Metadata
                };

                if (ttl.HasValue)
                {
                    document.TTL = DateTimeOffset.UtcNow.Add(ttl.Value).ToUnixTimeSeconds();
                }

                var savedResult = await _taskRepository.PutAsync(document);
                return savedResult != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<TaskExecutionResult?> GetTaskResultAsync(string sessionId, string taskId)
        {
            try
            {
                var document = await _taskRepository.GetAsync(sessionId, taskId);
                return document?.ToExecutionResult();
            }
            catch
            {
                return null;
            }
        }

        public async Task<IList<string>> GetActiveSessionsAsync()
        {
            try
            {
                // This would require a scan operation in DynamoDB, which is expensive
                // In a production system, you might want to maintain an index or use a different approach
                var activeSessions = new List<string>();
                
                // For now, return empty list - this would need to be implemented based on specific requirements
                // You might want to use a GSI on status or maintain a separate active sessions table
                
                return activeSessions;
            }
            catch
            {
                return new List<string>();
            }
        }

        public async Task<bool> ExtendSessionTtlAsync(string sessionId, TimeSpan ttl)
        {
            try
            {
                var document = await _executionRepository.GetAsync(sessionId);
                if (document == null)
                {
                    return false;
                }

                document.SetTTL(ttl);

                var result = await _executionRepository.PutAsync(document);
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<int> CleanupExpiredSessionsAsync()
        {
            // DynamoDB handles TTL cleanup automatically
            // This method could be used for additional cleanup logic if needed
            await Task.CompletedTask;
            return 0;
        }

        /// <summary>
        /// Clean up related node and task execution data for a session.
        /// </summary>
        private async Task CleanupRelatedDataAsync(string sessionId)
        {
            try
            {
                // Note: This is a simplified cleanup
                // In a production system, you might want to use batch operations or background jobs
                
                // For now, we'll skip the cleanup as it would require scanning operations
                // which are expensive in DynamoDB
                await Task.CompletedTask;
            }
            catch
            {
                // Ignore cleanup errors
            }
        }
    }
}
