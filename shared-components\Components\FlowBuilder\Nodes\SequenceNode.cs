using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Nodes
{
    /// <summary>
    /// Sequence Node - Executes child nodes in sequential order.
    /// Has 1 input (input handle on left) and configurable outputs (default 2, can be increased).
    /// </summary>
    public class SequenceNode : BaseFlowNode
    {
        private SequenceNodeDefinition? _nodeConfig;

        public SequenceNode()
        {
            NodeType = "Sequence";
        }

        protected override void InitializeHandles()
        {
            _inputHandles.Clear();
            _outputHandles.Clear();

            // Single input handle on the left
            _inputHandles.Add(new NodeHandle
            {
                Id = "input",
                Name = "Input",
                Type = NodeHandleType.Input,
                Position = "left"
            });

            // Multiple output handles on the right
            var outputCount = _nodeConfig?.OutputCount ?? 2;
            for (int i = 0; i < outputCount; i++)
            {
                _outputHandles.Add(new NodeHandle
                {
                    Id = $"output-{i}",
                    Name = $"Output {i + 1}",
                    Type = NodeHandleType.Output,
                    Position = "right"
                });
            }

            InputHandles = _inputHandles.AsReadOnly();
            OutputHandles = _outputHandles.AsReadOnly();
        }

        protected override async Task<bool> InitializeNodeSpecificAsync(FlowNodeDefinition definition)
        {
            try
            {
                _nodeConfig = new SequenceNodeDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Position = definition.Position,
                    Configuration = definition.Configuration,
                    Conditions = definition.Conditions,
                    Metadata = definition.Metadata,
                    OutputCount = definition.GetConfigValue<int>("outputCount", 2)
                };

                // Reinitialize handles with the correct output count
                InitializeHandles();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateNodeSpecificAsync(FlowValidationContext context, NodeValidationResult result)
        {
            await Task.CompletedTask;

            if (_nodeConfig == null)
            {
                result.Errors.Add("Node configuration is not initialized");
                return;
            }

            // Validate output count
            if (_nodeConfig.OutputCount < 1)
            {
                result.Errors.Add("Output count must be at least 1");
            }

            if (_nodeConfig.OutputCount > 10)
            {
                result.Warnings.Add("Output count is very high, consider if this is intentional");
            }

            // Validate that there are enough connected nodes for sequential execution
            var connectedOutputs = context.AllEdges
                .Where(e => e.Source == NodeId)
                .Select(e => e.SourceHandle)
                .Distinct()
                .Count();

            if (connectedOutputs == 0)
            {
                result.Warnings.Add("Sequence node has no connected outputs");
            }
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_nodeConfig == null)
                {
                    return CreateFailureResult("Node configuration is not initialized");
                }

                // Check if conditions are met
                if (!await EvaluateConditionsAsync(context))
                {
                    return new NodeExecutionResult
                    {
                        NodeId = NodeId,
                        Status = NodeExecutionStatus.Skipped,
                        CompletedAt = DateTime.UtcNow,
                        ExecutionTime = DateTime.UtcNow - startTime
                    };
                }

                // For sequence nodes, the execution logic is primarily handled by the flow engine
                // The node itself just indicates that it's ready to proceed with sequential execution
                var outputData = new Dictionary<string, object>
                {
                    ["executionMode"] = "sequence",
                    ["outputCount"] = _nodeConfig.OutputCount,
                    ["sequenceOrder"] = Enumerable.Range(0, _nodeConfig.OutputCount).ToList()
                };

                // Store sequence information in execution context
                context.ExecutionData["sequenceNodeId"] = NodeId;
                context.ExecutionData["sequenceOutputCount"] = _nodeConfig.OutputCount;
                context.ExecutionData["sequenceCurrentIndex"] = 0;

                // The flow engine will use this information to execute connected nodes sequentially
                var result = CreateSuccessResult(outputData);
                result.ExecutionTime = DateTime.UtcNow - startTime;

                // Add metadata about sequence execution
                result.Metadata["executionMode"] = "sequence";
                result.Metadata["requiresSequentialExecution"] = true;

                return result;
            }
            catch (Exception ex)
            {
                return CreateFailureResult($"Error executing Sequence node: {ex.Message}", ex);
            }
        }

        public override async Task<IList<NextNodeInfo>> GetNextNodesAsync(NodeExecutionResult executionResult, FlowExecutionContext context)
        {
            var nextNodes = new List<NextNodeInfo>();

            if (executionResult.Status == NodeExecutionStatus.Completed && _nodeConfig != null)
            {
                // For sequence nodes, we need to return the nodes in the order they should be executed
                // The flow engine will handle the sequential execution logic
                for (int i = 0; i < _nodeConfig.OutputCount; i++)
                {
                    var outputHandle = $"output-{i}";
                    
                    // The actual next node IDs would be determined by the flow engine
                    // based on the edges connected to this sequence node
                    if (executionResult.NextNodeIds.Count > i)
                    {
                        nextNodes.Add(new NextNodeInfo
                        {
                            NodeId = executionResult.NextNodeIds[i],
                            OutputHandle = outputHandle,
                            InputHandle = "input",
                            TransferData = new Dictionary<string, object>
                            {
                                ["sequenceIndex"] = i,
                                ["sequenceTotal"] = _nodeConfig.OutputCount,
                                ["isSequentialExecution"] = true
                            }
                        });
                    }
                }
            }

            return nextNodes;
        }

        /// <summary>
        /// Gets the configured output count.
        /// </summary>
        public int GetOutputCount()
        {
            return _nodeConfig?.OutputCount ?? 2;
        }
    }
}
