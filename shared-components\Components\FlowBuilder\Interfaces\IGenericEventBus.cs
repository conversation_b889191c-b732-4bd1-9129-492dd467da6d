using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Interfaces
{
    /// <summary>
    /// Generic event bus interface for handling flow execution events and message queuing.
    /// This is separate from APIEventBus and designed for internal flow processing.
    /// </summary>
    public interface IGenericEventBus
    {
        /// <summary>
        /// Sends a message to the event bus for processing.
        /// </summary>
        /// <param name="message">The message to send</param>
        /// <param name="delayInSeconds">Optional delay before processing</param>
        /// <returns>Message ID for tracking</returns>
        Task<string> SendMessageAsync(GenericEventMessage message, int delayInSeconds = 0);

        /// <summary>
        /// Sends a message to a specific queue.
        /// </summary>
        /// <param name="queueName">The target queue name</param>
        /// <param name="message">The message to send</param>
        /// <param name="delayInSeconds">Optional delay before processing</param>
        /// <returns>Message ID for tracking</returns>
        Task<string> SendToQueueAsync(string queueName, GenericEventMessage message, int delayInSeconds = 0);

        /// <summary>
        /// Subscribes to messages of a specific type.
        /// </summary>
        /// <param name="messageType">The message type to subscribe to</param>
        /// <param name="handler">The handler function for processing messages</param>
        void Subscribe(string messageType, Func<GenericEventMessage, Task<bool>> handler);

        /// <summary>
        /// Subscribes to messages from a specific queue.
        /// </summary>
        /// <param name="queueName">The queue name to subscribe to</param>
        /// <param name="handler">The handler function for processing messages</param>
        void SubscribeToQueue(string queueName, Func<GenericEventMessage, Task<bool>> handler);

        /// <summary>
        /// Unsubscribes from messages of a specific type.
        /// </summary>
        /// <param name="messageType">The message type to unsubscribe from</param>
        /// <param name="handler">The handler function to remove</param>
        void Unsubscribe(string messageType, Func<GenericEventMessage, Task<bool>> handler);

        /// <summary>
        /// Starts the event bus message processing.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task StartAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Stops the event bus message processing.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        Task StopAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the status of a message by its ID.
        /// </summary>
        /// <param name="messageId">The message ID</param>
        /// <returns>Message processing status</returns>
        Task<MessageStatus> GetMessageStatusAsync(string messageId);

        /// <summary>
        /// Event fired when a message is processed successfully.
        /// </summary>
        event EventHandler<MessageProcessedEventArgs>? OnMessageProcessed;

        /// <summary>
        /// Event fired when message processing fails.
        /// </summary>
        event EventHandler<MessageFailedEventArgs>? OnMessageFailed;
    }

    /// <summary>
    /// Generic event message for the event bus system.
    /// </summary>
    public class GenericEventMessage
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string MessageType { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public string Target { get; set; } = string.Empty;
        public object Payload { get; set; } = new();
        public Dictionary<string, string> Headers { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ProcessAt { get; set; }
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;
        public string? CorrelationId { get; set; }
        public string? SessionId { get; set; }
    }

    /// <summary>
    /// Message processing status.
    /// </summary>
    public enum MessageStatus
    {
        Pending,
        Processing,
        Completed,
        Failed,
        Cancelled,
        Retrying
    }

    /// <summary>
    /// Event arguments for successful message processing.
    /// </summary>
    public class MessageProcessedEventArgs : EventArgs
    {
        public string MessageId { get; set; } = string.Empty;
        public string MessageType { get; set; } = string.Empty;
        public TimeSpan ProcessingTime { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Event arguments for failed message processing.
    /// </summary>
    public class MessageFailedEventArgs : EventArgs
    {
        public string MessageId { get; set; } = string.Empty;
        public string MessageType { get; set; } = string.Empty;
        public Exception Exception { get; set; } = new();
        public int RetryCount { get; set; }
        public bool WillRetry { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
}
