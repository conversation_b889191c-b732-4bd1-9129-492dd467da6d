using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Nodes
{
    /// <summary>
    /// Switch Node - Conditional branching with multiple conditions.
    /// Has 1 input (input handle on left) and N+1 outputs (N condition outputs + 1 default output).
    /// </summary>
    public class SwitchNode : BaseFlowNode
    {
        private SwitchNodeDefinition? _nodeConfig;

        public SwitchNode()
        {
            NodeType = "Switch";
        }

        protected override void InitializeHandles()
        {
            _inputHandles.Clear();
            _outputHandles.Clear();

            // Single input handle on the left
            _inputHandles.Add(new NodeHandle
            {
                Id = "input",
                Name = "Input",
                Type = NodeHandleType.Input,
                Position = "left"
            });

            // Condition output handles
            if (_nodeConfig?.Conditions != null)
            {
                for (int i = 0; i < _nodeConfig.Conditions.Count; i++)
                {
                    var condition = _nodeConfig.Conditions[i];
                    _outputHandles.Add(new NodeHandle
                    {
                        Id = $"condition-{i}",
                        Name = condition.Label,
                        Type = NodeHandleType.Output,
                        Position = "right"
                    });
                }
            }

            // Default output handle (when no conditions match)
            _outputHandles.Add(new NodeHandle
            {
                Id = "default",
                Name = "Default",
                Type = NodeHandleType.Output,
                Position = "right"
            });

            InputHandles = _inputHandles.AsReadOnly();
            OutputHandles = _outputHandles.AsReadOnly();
        }

        protected override async Task<bool> InitializeNodeSpecificAsync(FlowNodeDefinition definition)
        {
            try
            {
                _nodeConfig = new SwitchNodeDefinition
                {
                    Id = definition.Id,
                    Type = definition.Type,
                    Name = definition.Name,
                    Position = definition.Position,
                    Configuration = definition.Configuration,
                    Conditions = definition.Conditions,
                    Metadata = definition.Metadata
                };

                // Extract switch conditions from configuration
                if (definition.Configuration.ContainsKey("conditions"))
                {
                    var conditionsData = definition.Configuration["conditions"];
                    if (conditionsData is List<SwitchCondition> conditions)
                    {
                        _nodeConfig.Conditions = conditions;
                    }
                }

                // Reinitialize handles with the correct conditions
                InitializeHandles();

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        protected override async Task ValidateNodeSpecificAsync(FlowValidationContext context, NodeValidationResult result)
        {
            await Task.CompletedTask;

            if (_nodeConfig == null)
            {
                result.Errors.Add("Node configuration is not initialized");
                return;
            }

            // Validate conditions
            if (_nodeConfig.Conditions == null || _nodeConfig.Conditions.Count == 0)
            {
                result.Errors.Add("Switch node must have at least one condition");
                return;
            }

            if (_nodeConfig.Conditions.Count > 20)
            {
                result.Warnings.Add("Switch node has many conditions, consider if this is optimal");
            }

            // Validate each condition
            for (int i = 0; i < _nodeConfig.Conditions.Count; i++)
            {
                var condition = _nodeConfig.Conditions[i];
                
                if (string.IsNullOrEmpty(condition.Id))
                {
                    result.Errors.Add($"Condition {i} must have an ID");
                }

                if (string.IsNullOrEmpty(condition.Label))
                {
                    result.Warnings.Add($"Condition {i} should have a descriptive label");
                }

                // Validate condition rules
                ValidateRuleGroup(condition.Condition, context, result, $"Condition {i}");
            }

            // Validate connections
            var conditionConnections = context.AllEdges
                .Where(e => e.Source == NodeId && e.SourceHandle?.StartsWith("condition-") == true)
                .Count();

            var defaultConnections = context.AllEdges
                .Where(e => e.Source == NodeId && e.SourceHandle == "default")
                .Count();

            if (conditionConnections == 0)
            {
                result.Warnings.Add("Switch node has no condition connections");
            }

            if (defaultConnections == 0)
            {
                result.Warnings.Add("Switch node has no default connection - consider adding fallback path");
            }
        }

        public override async Task<NodeExecutionResult> ExecuteAsync(FlowExecutionContext context, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;

            try
            {
                if (_nodeConfig == null)
                {
                    return CreateFailureResult("Node configuration is not initialized");
                }

                // Check if conditions are met
                if (!await EvaluateConditionsAsync(context))
                {
                    return new NodeExecutionResult
                    {
                        NodeId = NodeId,
                        Status = NodeExecutionStatus.Skipped,
                        CompletedAt = DateTime.UtcNow,
                        ExecutionTime = DateTime.UtcNow - startTime
                    };
                }

                // Evaluate each condition in order
                string? selectedPath = null;
                int selectedConditionIndex = -1;

                for (int i = 0; i < _nodeConfig.Conditions.Count; i++)
                {
                    var condition = _nodeConfig.Conditions[i];
                    bool conditionMet = await EvaluateRuleGroupAsync(condition.Condition, context);

                    if (conditionMet)
                    {
                        selectedPath = $"condition-{i}";
                        selectedConditionIndex = i;
                        break;
                    }
                }

                // If no condition matched, use default path
                if (selectedPath == null)
                {
                    selectedPath = "default";
                }

                var outputData = new Dictionary<string, object>
                {
                    ["executionMode"] = "switch",
                    ["selectedPath"] = selectedPath,
                    ["selectedConditionIndex"] = selectedConditionIndex,
                    ["totalConditions"] = _nodeConfig.Conditions.Count
                };

                // Store switch execution information
                context.ExecutionData["switchNodeId"] = NodeId;
                context.ExecutionData["switchSelectedPath"] = selectedPath;
                context.ExecutionData["switchSelectedConditionIndex"] = selectedConditionIndex;

                // Set variable fields for switch state
                context.SetFieldValue($"switch.{NodeId}.selectedPath", selectedPath);
                context.SetFieldValue($"switch.{NodeId}.selectedConditionIndex", selectedConditionIndex);

                if (selectedConditionIndex >= 0)
                {
                    var selectedCondition = _nodeConfig.Conditions[selectedConditionIndex];
                    context.SetFieldValue($"switch.{NodeId}.selectedConditionLabel", selectedCondition.Label);
                }

                var result = CreateSuccessResult(outputData);
                result.ExecutionTime = DateTime.UtcNow - startTime;

                // Add metadata about switch execution
                result.Metadata["executionMode"] = "switch";
                result.Metadata["selectedPath"] = selectedPath;
                result.Metadata["selectedConditionIndex"] = selectedConditionIndex;

                // Set the next node based on selected path
                result.NextNodeIds = new List<string> { selectedPath };

                return result;
            }
            catch (Exception ex)
            {
                return CreateFailureResult($"Error executing Switch node: {ex.Message}", ex);
            }
        }

        public override async Task<IList<NextNodeInfo>> GetNextNodesAsync(NodeExecutionResult executionResult, FlowExecutionContext context)
        {
            var nextNodes = new List<NextNodeInfo>();

            if (executionResult.Status == NodeExecutionStatus.Completed)
            {
                var selectedPath = executionResult.Metadata.GetValueOrDefault("selectedPath", "default")?.ToString();
                var selectedConditionIndex = (int)(executionResult.Metadata.GetValueOrDefault("selectedConditionIndex", -1) ?? -1);

                if (!string.IsNullOrEmpty(selectedPath))
                {
                    nextNodes.Add(new NextNodeInfo
                    {
                        NodeId = selectedPath, // This will be resolved by the flow engine
                        OutputHandle = selectedPath,
                        InputHandle = "input",
                        TransferData = new Dictionary<string, object>
                        {
                            ["isSwitchExecution"] = true,
                            ["switchPath"] = selectedPath,
                            ["switchConditionIndex"] = selectedConditionIndex,
                            ["switchNodeId"] = NodeId
                        }
                    });
                }
            }

            return nextNodes;
        }

        /// <summary>
        /// Validates a rule group and its nested rules.
        /// </summary>
        private void ValidateRuleGroup(FlowRuleGroup ruleGroup, FlowValidationContext context, NodeValidationResult result, string conditionName)
        {
            foreach (var rule in ruleGroup.Rules)
            {
                if (!context.AvailableFields.ContainsKey(rule.Field))
                {
                    result.Warnings.Add($"{conditionName} references unknown field: {rule.Field}");
                }
            }

            foreach (var nestedGroup in ruleGroup.Groups)
            {
                ValidateRuleGroup(nestedGroup, context, result, conditionName);
            }
        }

        /// <summary>
        /// Gets the configured conditions.
        /// </summary>
        public List<SwitchCondition> GetConditions()
        {
            return _nodeConfig?.Conditions ?? new List<SwitchCondition>();
        }

        /// <summary>
        /// Gets the number of condition outputs.
        /// </summary>
        public int GetConditionCount()
        {
            return _nodeConfig?.Conditions?.Count ?? 0;
        }
    }
}
