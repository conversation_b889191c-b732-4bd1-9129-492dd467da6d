using shared.Components.FlowBuilder.Models;

namespace shared.Components.FlowBuilder.Interfaces
{
    /// <summary>
    /// Interface for managing flow execution state. Designed to work with both
    /// in-memory storage (like Redis) and NoSQL databases.
    /// </summary>
    public interface IFlowStateService
    {
        /// <summary>
        /// Stores the execution state for a flow session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="state">The execution state to store</param>
        /// <param name="ttl">Optional time-to-live for the state</param>
        /// <returns>True if storage was successful</returns>
        Task<bool> StoreExecutionStateAsync(string sessionId, FlowExecutionState state, TimeSpan? ttl = null);

        /// <summary>
        /// Retrieves the execution state for a flow session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <returns>The execution state or null if not found</returns>
        Task<FlowExecutionState?> GetExecutionStateAsync(string sessionId);

        /// <summary>
        /// Updates specific fields in the execution state.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="updates">Dictionary of field updates</param>
        /// <returns>True if update was successful</returns>
        Task<bool> UpdateExecutionStateAsync(string sessionId, Dictionary<string, object> updates);

        /// <summary>
        /// Deletes the execution state for a flow session.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <returns>True if deletion was successful</returns>
        Task<bool> DeleteExecutionStateAsync(string sessionId);

        /// <summary>
        /// Stores field values for a flow execution.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="fields">Dictionary of field values</param>
        /// <param name="ttl">Optional time-to-live for the fields</param>
        /// <returns>True if storage was successful</returns>
        Task<bool> StoreFieldValuesAsync(string sessionId, Dictionary<string, object> fields, TimeSpan? ttl = null);

        /// <summary>
        /// Retrieves field values for a flow execution.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <returns>Dictionary of field values</returns>
        Task<Dictionary<string, object>> GetFieldValuesAsync(string sessionId);

        /// <summary>
        /// Updates specific field values.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="fieldUpdates">Dictionary of field updates</param>
        /// <returns>True if update was successful</returns>
        Task<bool> UpdateFieldValuesAsync(string sessionId, Dictionary<string, object> fieldUpdates);

        /// <summary>
        /// Gets the value of a specific field.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="fieldName">The field name</param>
        /// <returns>The field value or null if not found</returns>
        Task<object?> GetFieldValueAsync(string sessionId, string fieldName);

        /// <summary>
        /// Sets the value of a specific field.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="fieldName">The field name</param>
        /// <param name="value">The field value</param>
        /// <returns>True if update was successful</returns>
        Task<bool> SetFieldValueAsync(string sessionId, string fieldName, object value);

        /// <summary>
        /// Stores node execution results.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="nodeId">The node ID</param>
        /// <param name="result">The execution result</param>
        /// <param name="ttl">Optional time-to-live</param>
        /// <returns>True if storage was successful</returns>
        Task<bool> StoreNodeResultAsync(string sessionId, string nodeId, NodeExecutionResult result, TimeSpan? ttl = null);

        /// <summary>
        /// Retrieves node execution results.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="nodeId">The node ID</param>
        /// <returns>The execution result or null if not found</returns>
        Task<NodeExecutionResult?> GetNodeResultAsync(string sessionId, string nodeId);

        /// <summary>
        /// Stores task execution results.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="taskId">The task ID</param>
        /// <param name="result">The execution result</param>
        /// <param name="ttl">Optional time-to-live</param>
        /// <returns>True if storage was successful</returns>
        Task<bool> StoreTaskResultAsync(string sessionId, string taskId, TaskExecutionResult result, TimeSpan? ttl = null);

        /// <summary>
        /// Retrieves task execution results.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="taskId">The task ID</param>
        /// <returns>The execution result or null if not found</returns>
        Task<TaskExecutionResult?> GetTaskResultAsync(string sessionId, string taskId);

        /// <summary>
        /// Lists all active execution sessions.
        /// </summary>
        /// <returns>List of active session IDs</returns>
        Task<IList<string>> GetActiveSessionsAsync();

        /// <summary>
        /// Extends the time-to-live for a session's data.
        /// </summary>
        /// <param name="sessionId">The execution session ID</param>
        /// <param name="ttl">New time-to-live</param>
        /// <returns>True if extension was successful</returns>
        Task<bool> ExtendSessionTtlAsync(string sessionId, TimeSpan ttl);

        /// <summary>
        /// Cleans up expired session data.
        /// </summary>
        /// <returns>Number of sessions cleaned up</returns>
        Task<int> CleanupExpiredSessionsAsync();
    }
}
